/**
 * 缓存模块 - 提供服务器端和客户端缓存功能
 */

// 服务器端内存缓存对象
class MemoryCache {
  constructor() {
    this.cache = new Map();
    this.defaultTTL = 10 * 60 * 1000; // 默认缓存10分钟
  }

  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} ttl - 过期时间(毫秒)，默认10分钟
   */
  set(key, value, ttl = this.defaultTTL) {
    const expiry = Date.now() + ttl;
    this.cache.set(key, { value, expiry });
    return true;
  }

  /**
   * 获取缓存项
   * @param {string} key - 缓存键
   * @returns {any} 缓存值，如果不存在或已过期则返回undefined
   */
  get(key) {
    const item = this.cache.get(key);
    if (!item) return undefined;
    
    // 检查是否过期
    if (Date.now() > item.expiry) {
      this.delete(key);
      return undefined;
    }
    
    return item.value;
  }

  /**
   * 删除缓存项
   * @param {string} key - 缓存键
   * @returns {boolean} 是否成功删除
   */
  delete(key) {
    return this.cache.delete(key);
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear();
  }

  /**
   * 获取所有缓存键
   * @returns {string[]} 缓存键数组
   */
  keys() {
    return Array.from(this.cache.keys());
  }

  /**
   * 获取缓存项数量
   * @returns {number} 缓存项数量
   */
  size() {
    return this.cache.size;
  }
}

// 客户端缓存 - 使用localStorage持久化
class ClientCache {
  constructor() {
    this.prefix = 'app_cache_';
    this.defaultTTL = 30 * 60 * 1000; // 默认30分钟
  }

  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} ttl - 过期时间(毫秒)
   */
  set(key, value, ttl = this.defaultTTL) {
    if (typeof window === 'undefined') return false;
    
    try {
      const item = {
        value,
        expiry: Date.now() + ttl
      };
      
      localStorage.setItem(this.prefix + key, JSON.stringify(item));
      return true;
    } catch (error) {
      console.error('客户端缓存错误:', error);
      return false;
    }
  }

  /**
   * 获取缓存项
   * @param {string} key - 缓存键
   * @returns {any} 缓存值，如果不存在或已过期则返回undefined
   */
  get(key) {
    if (typeof window === 'undefined') return undefined;
    
    try {
      const itemStr = localStorage.getItem(this.prefix + key);
      if (!itemStr) return undefined;
      
      const item = JSON.parse(itemStr);
      
      // 检查是否过期
      if (Date.now() > item.expiry) {
        this.delete(key);
        return undefined;
      }
      
      return item.value;
    } catch (error) {
      console.error('客户端缓存读取错误:', error);
      return undefined;
    }
  }

  /**
   * 删除缓存项
   * @param {string} key - 缓存键
   */
  delete(key) {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(this.prefix + key);
    } catch (error) {
      console.error('客户端缓存删除错误:', error);
    }
  }

  /**
   * 清空所有缓存
   */
  clear() {
    if (typeof window === 'undefined') return;
    
    try {
      Object.keys(localStorage)
        .filter(key => key.startsWith(this.prefix))
        .forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.error('客户端缓存清理错误:', error);
    }
  }
}

// 导出服务器端缓存单例
const memoryCache = new MemoryCache();

// 导出客户端缓存单例
const clientCache = new ClientCache();

// 导出模块
module.exports = { 
  memoryCache,
  clientCache
}; 