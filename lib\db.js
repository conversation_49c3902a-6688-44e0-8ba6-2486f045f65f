/**
 * PostgreSQL数据库连接模块
 */
const { Pool } = require('pg');
const { neon } = require('@neondatabase/serverless');
const fs = require('fs');
const path = require('path');
let dotenv;
try {
  dotenv = require('dotenv');
} catch (err) {
  console.warn('未安装dotenv，无法加载.env文件');
}

// 尝试加载env.txt文件中的环境变量
try {
  const envPath = path.resolve(__dirname, '../env.txt');
  if (fs.existsSync(envPath)) {
    console.log(`加载环境变量文件: ${envPath}`);
    const envContent = fs.readFileSync(envPath, 'utf-8');
    if (dotenv) {
      const envVars = dotenv.parse(envContent);
      Object.entries(envVars).forEach(([key, value]) => {
        if (!process.env[key]) {
          process.env[key] = value;
        }
      });
    } else {
      // 手动解析并设置环境变量
      envContent.split('\n').forEach(line => {
        if (line && !line.startsWith('#')) {
          const [key, value] = line.split('=');
          if (key && value && !process.env[key]) {
            process.env[key] = value.trim();
          }
        }
      });
    }
  }
} catch (err) {
  console.warn('加载环境变量文件失败:', err.message);
}

// 从环境变量获取数据库连接信息
const connectionString = process.env.DATABASE_URL || 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// 创建Neon SQL客户端
let sql;
try {
  sql = neon(connectionString);
  console.log('Neon SQL客户端初始化成功');
} catch (err) {
  console.error('Neon SQL客户端初始化失败:', err.message);
  // 创建一个替代函数，在异常情况下返回模拟数据
  sql = async (query, ...params) => {
    console.error(`SQL查询失败 (serverless): ${query}`);
    console.error('使用模拟数据替代...');
    return [];
  };
}

// 创建连接池，针对Neon数据库优化连接参数
const pool = new Pool({
  connectionString,
  ssl: { rejectUnauthorized: false }, // Neon需要SSL连接
  max: 5, // 进一步降低最大连接数，Neon免费版有连接限制
  min: 1, // 减少最小连接数
  idleTimeoutMillis: 30000, // 减少空闲超时时间到30秒，避免长时间占用连接
  connectionTimeoutMillis: 15000, // 增加连接超时时间到15秒
  acquireTimeoutMillis: 20000, // 获取连接的超时时间
  createTimeoutMillis: 20000, // 创建连接的超时时间
  destroyTimeoutMillis: 5000, // 销毁连接的超时时间
  reapIntervalMillis: 1000, // 检查空闲连接的间隔
  createRetryIntervalMillis: 200, // 创建连接重试间隔
  allowExitOnIdle: true, // 允许应用退出时关闭空闲连接
  keepAlive: true, // 保持连接活跃
  keepAliveInitialDelayMillis: 5000, // 5秒后开始发送keepalive包
  statement_timeout: 30000, // SQL语句执行超时时间
  query_timeout: 30000, // 查询超时时间
});

// 连接池状态监控
let isPoolHealthy = true; // 连接池健康状态标志
let lastErrorTime = 0; // 最后一次错误时间
const ERROR_THRESHOLD = 5 * 60 * 1000; // 5分钟内的错误计数阈值
let connectionAttempts = 0; // 连接尝试次数
const MAX_CONNECTION_ATTEMPTS = 5; // 最大连接尝试次数

// 连接错误处理，改进以避免应用崩溃
pool.on('error', (err) => {
  console.error('[DB] Unexpected error on idle PostgreSQL client', err);
  isPoolHealthy = false;
  lastErrorTime = Date.now();
  connectionAttempts++;

  // 不再直接退出进程，而是标记连接池状态
  // process.exit(-1); // 移除这行以避免应用崩溃

  if (connectionAttempts < MAX_CONNECTION_ATTEMPTS) {
    // 尝试在短暂延迟后恢复连接池
    const retryDelay = Math.min(5000 * Math.pow(2, connectionAttempts - 1), 60000); // 指数退避策略
    console.log(`[DB] 将在 ${retryDelay/1000} 秒后尝试恢复数据库连接池...`);

    setTimeout(() => {
      console.log('[DB] 尝试恢复数据库连接池...');
      testConnection().then(isConnected => {
        isPoolHealthy = isConnected;
        if (isConnected) {
          connectionAttempts = 0;
          console.log('[DB] 数据库连接已恢复!');
        }
      }).catch(error => {
        console.error('[DB] 恢复连接失败:', error.message);
      });
    }, retryDelay);
  } else {
    console.error(`[DB] 达到最大连接尝试次数 (${MAX_CONNECTION_ATTEMPTS})，停止尝试自动重连。请检查数据库配置。`);
  }
});

// 测试连接
async function testConnection() {
  try {
    const client = await pool.connect();
    try {
      await client.query('SELECT 1');
      return true;
    } finally {
      client.release();
    }
  } catch (err) {
    console.error('[DB] 测试连接失败:', err.message);
    return false;
  }
}

// 添加连接池健康检查
pool.on('connect', (client) => {
  console.log('[DB] 新的数据库连接已建立');
  isPoolHealthy = true;
  connectionAttempts = 0; // 重置连接尝试计数
});

// 模拟数据，在数据库连接失败时使用
const mockData = {
  categories: [
    { id: 1, name: '室内游乐场', slug: 'indoor-playground', is_featured: true, featured_order: 1 },
    { id: 2, name: '蹦床公园', slug: 'trampoline-park', is_featured: true, featured_order: 2 },
    { id: 3, name: '互动设备', slug: 'interactive-equipment', is_featured: true, featured_order: 3 }
  ],
  products: [
    {
      id: 1,
      name: 'KTV互动娱乐系统',
      slug: 'ktv-interactive-system',
      description: '高科技智能KTV互动娱乐系统，集成点歌、游戏、社交等多种功能',
      category: '互动设备',
      image_url: '/images/products/ktv-system-pro-main.jpg',
      images: ['/images/products/ktv-system-pro-main.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 2,
      name: 'AR体感蹦床系统',
      slug: 'ar-motion-trampoline',
      description: 'AR增强现实体感蹦床系统，结合虚拟现实技术与体感运动',
      category: '互动设备',
      image_url: '/images/products/ar-trampoline-pro-main.jpg',
      images: ['/images/products/ar-trampoline-pro-main.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 3,
      name: '3D电子沙盘系统',
      slug: '3d-electronic-sandbox',
      description: '互动式3D电子沙盘系统，通过投影技术创造沉浸式地形互动体验',
      category: '互动设备',
      image_url: '/images/products/3d-sandbox-pro-main.jpg',
      images: ['/images/products/3d-sandbox-pro-main.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 4,
      name: '互动足球训练系统',
      slug: 'interactive-football-system',
      description: '智能互动足球训练系统，结合投影技术和体感识别',
      category: '互动设备',
      image_url: '/images/products/interactive-football-pro-main.jpg',
      images: ['/images/products/interactive-football-pro-main.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 5,
      name: '体感攀岩互动系统',
      slug: 'motion-sensing-climbing',
      description: '体感攀岩互动系统，通过投影和体感技术提供安全刺激的攀岩体验',
      category: '互动设备',
      image_url: '/images/products/motion-climbing-pro-extra-1.jpg',
      images: ['/images/products/motion-climbing-pro-extra-1.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 6,
      name: '互动砸球游戏系统',
      slug: 'interactive-ball-smash',
      description: '互动砸球游戏系统，通过投影和体感技术让玩家体验刺激有趣的砸球游戏',
      category: '互动设备',
      image_url: '/images/products/interactive-ball-pro-main.jpg',
      images: ['/images/products/interactive-ball-pro-main.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 7,
      name: 'AR教育互动系统',
      slug: 'ar-education-system',
      description: 'AR增强现实教育系统，通过虚拟与现实的结合为学生提供生动有趣的学习体验',
      category: '互动设备',
      image_url: '/images/products/ar-education-pro-main.jpg',
      images: ['/images/products/ar-education-pro-main.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 8,
      name: '保龄球互动娱乐系统',
      slug: 'bowling-interactive-system',
      description: '智能保龄球互动娱乐系统，集成多种游戏模式和社交功能',
      category: '互动设备',
      image_url: '/images/products/bowling-system-v2-main.jpg',
      images: ['/images/products/bowling-system-v2-main.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 9,
      name: '全息投影餐厅系统',
      slug: 'holographic-dining-system',
      description: '全息投影餐厅系统，为用餐体验增添科技感和娱乐性',
      category: '互动设备',
      image_url: '/images/products/holographic-dining-v2-extra-1.jpg',
      images: ['/images/products/holographic-dining-v2-extra-1.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 10,
      name: '全息投影桌面系统',
      slug: 'holographic-table-system',
      description: '全息投影桌面系统，提供互动展示和娱乐功能',
      category: '互动设备',
      image_url: '/images/products/holographic-table-v2-main.jpg',
      images: ['/images/products/holographic-table-v2-main.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 11,
      name: '儿童互动砸球系统',
      slug: 'children-interactive-ball',
      description: '专为儿童设计的互动砸球游戏系统，安全有趣',
      category: '互动设备',
      image_url: '/images/products/children-ball-v2-main.jpg',
      images: ['/images/products/children-ball-v2-main.jpg'],
      is_featured: true,
      is_published: true
    },
    {
      id: 12,
      name: '儿童互动沙滩系统',
      slug: 'children-interactive-beach',
      description: '儿童互动沙滩游戏系统，模拟真实沙滩体验',
      category: '互动设备',
      image_url: '/images/products/children-beach-v2-main.jpg',
      images: ['/images/products/children-beach-v2-main.jpg'],
      is_featured: true,
      is_published: true
    }
  ]
};

// 检查连接池健康状态
function isConnectionHealthy() {
  // 如果上次错误在阈值时间内，且标记为不健康，则返回false
  if (!isPoolHealthy && (Date.now() - lastErrorTime < ERROR_THRESHOLD)) {
    return false;
  }
  return true;
}

// 执行查询的通用函数，添加重试和模拟数据支持
async function query(text, params, options = {}) {
  const { retries = 2, mockOnFailure = true, mockResult = null, timeout = 25000 } = options;
  const start = Date.now();

  // 如果连接池不健康，且允许使用模拟数据，直接返回模拟数据
  if (!isConnectionHealthy() && mockOnFailure && mockResult) {
    console.log(`[DB] 连接池不健康，使用模拟数据: ${text.substring(0, 60)}${text.length > 60 ? '...' : ''}`);
    return mockResult;
  }

  // 尝试执行查询，支持重试
  let lastError = null;
  for (let attempt = 0; attempt <= retries; attempt++) {
    let client = null;
    try {
      // 如果不是第一次尝试，添加小延迟
      if (attempt > 0) {
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // 递增延迟
        console.log(`[DB] 第${attempt}次重试查询...`);
      }

      // 使用Promise.race来实现超时控制
      const queryPromise = (async () => {
        client = await pool.connect();
        try {
          const res = await client.query(text, params);
          const duration = Date.now() - start;
          console.log(`[DB] Executed query in ${duration}ms: ${text.substring(0, 60)}${text.length > 60 ? '...' : ''}`);
          isPoolHealthy = true; // 查询成功，标记连接池为健康
          return res;
        } finally {
          if (client) {
            client.release();
            client = null;
          }
        }
      })();

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error(`Query timeout after ${timeout}ms`)), timeout);
      });

      return await Promise.race([queryPromise, timeoutPromise]);

    } catch (error) {
      lastError = error;
      console.error(`[DB] Error executing query (attempt ${attempt + 1}/${retries + 1}): ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`);
      console.error(`[DB] Error details:`, error);

      // 确保客户端被释放
      if (client) {
        try {
          client.release();
        } catch (releaseError) {
          console.error('[DB] Error releasing client:', releaseError);
        }
        client = null;
      }

      // 标记连接池状态
      if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT' ||
          error.message.includes('timeout') || error.message.includes('terminated') ||
          error.message.includes('Connection terminated')) {
        isPoolHealthy = false;
        lastErrorTime = Date.now();
      }

      // 如果已经尝试最大次数，且允许使用模拟数据
      if (attempt === retries && mockOnFailure) {
        console.log(`[DB] 使用模拟数据替代`);
        // 处理不同类型的查询，返回适当的模拟数据
        if (mockResult) {
          console.log(`[DB] 查询失败后使用提供的模拟数据`);
          return mockResult;
        } else if (text.toLowerCase().includes('from products') && text.toLowerCase().includes('where slug =')) {
          // 查询单个产品
          const slugParam = params ? params[0] : null;
          const product = slugParam ? mockData.products.find(p => p.slug === slugParam) : null;
          console.log(`[DB] 使用模拟产品数据: ${slugParam}`);
          return { rows: product ? [product] : [] };
        } else if (text.toLowerCase().includes('from products')) {
          // 查询产品列表
          console.log(`[DB] 使用模拟产品列表数据`);
          return { rows: mockData.products };
        } else if (text.toLowerCase().includes('from categories') && text.toLowerCase().includes('where is_featured')) {
          // 查询热门分类
          console.log(`[DB] 使用模拟热门分类数据`);
          return { rows: mockData.categories.filter(c => c.is_featured) };
        } else if (text.toLowerCase().includes('from categories')) {
          // 查询所有分类
          console.log(`[DB] 使用模拟分类数据`);
          return { rows: mockData.categories };
        } else if (text.toLowerCase().includes('count(*)')) {
          // 计数查询
          console.log(`[DB] 使用模拟计数数据`);
          return { rows: [{ total: mockData.products.length }] };
        }
      }
    }
  }

  // 所有重试都失败，抛出最后一个错误
  throw lastError;
}

// 获取健康状态
async function getHealthStatus() {
  if (!isConnectionHealthy()) {
    return {
      status: 'unhealthy',
      lastError: lastErrorTime ? new Date(lastErrorTime).toISOString() : null,
      timeSinceLastError: lastErrorTime ? Date.now() - lastErrorTime : null,
    };
  }

  try {
    // 尝试执行简单查询检查连接
    await query('SELECT 1', [], { retries: 0, mockOnFailure: false });
    return { status: 'healthy' };
  } catch (error) {
    isPoolHealthy = false;
    lastErrorTime = Date.now();
    return {
      status: 'unhealthy',
      error: error.message,
      lastError: new Date(lastErrorTime).toISOString(),
    };
  }
}

// 初始化数据库函数
async function initializeDatabase() {
  try {
    console.log('正在初始化数据库...');

    // 创建categories表
    await query(`
      CREATE TABLE IF NOT EXISTS categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT true,
        type VARCHAR(50) DEFAULT 'product_type',
        order_num INTEGER DEFAULT 0,
        is_featured BOOLEAN DEFAULT false,
        featured_order INTEGER DEFAULT 0,
        featured_type VARCHAR(50),
        translations JSONB,
        parent_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `, [], { retries: 3, mockOnFailure: false });

    // 创建products表
    await query(`
      CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT,
        image_url TEXT,
        images JSONB,
        category VARCHAR(255),
        categories JSONB,
        type VARCHAR(100),
        size VARCHAR(100),
        style VARCHAR(100),
        features JSONB,
        is_featured BOOLEAN DEFAULT false,
        is_published BOOLEAN DEFAULT false,
        in_stock BOOLEAN DEFAULT true,
        translations JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `, [], { retries: 3, mockOnFailure: false });

    // 创建users表
    await query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(100),
        role VARCHAR(20) DEFAULT 'user',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `, [], { retries: 3, mockOnFailure: false });

    // 创建表单提交表
    await query(`
      CREATE TABLE IF NOT EXISTS form_submissions (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(50),
        country VARCHAR(100),
        playground_size VARCHAR(100),
        product VARCHAR(255),
        message TEXT NOT NULL,
        status VARCHAR(50) DEFAULT 'new',
        is_read BOOLEAN DEFAULT FALSE,
        admin_notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `, [], { retries: 3, mockOnFailure: false });

    console.log('数据库初始化完成');
    return { success: true, message: '数据库初始化成功' };
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return { success: false, message: `数据库初始化失败: ${error.message}` };
  }
}

// 添加空的setupDatabase函数以保持兼容性
async function setupDatabase() {
  console.warn('setupDatabase is deprecated. Use initializeDatabase instead.');
  return await initializeDatabase();
}

// 导出所有函数，同时兼容ESM和CommonJS
if (typeof module !== 'undefined' && module.exports) {
  // CommonJS导出
  module.exports = {
    query,
    pool,
    sql,
    initializeDatabase,
    getHealthStatus,
    mockData,
    isConnectionHealthy,
    setupDatabase,
    testConnection,
    default: sql
  };
} else {
  // ESM导出 - 仅供参考，实际不执行这部分代码
  // 因为当前文件是CommonJS模块
  console.warn('ESM导出路径不应该被执行');
  /*
  export {
    query,
    pool,
    sql,
    initializeDatabase,
    getHealthStatus,
    mockData,
    isConnectionHealthy,
    setupDatabase,
    testConnection
  };

  // 默认导出
  export default sql;
  */
}
